const xr = wx.getXrFrameSystem();
var markerTransform = require('../../pages/xr-template-markerLock/utils/markerTransform');
const CameraStateManager = require('./camera-state')
let prevPos = null

Component({
  properties: {
    initialVpsTracked: Boolean,
    unfinishedItemsCountRaw: Number,
    transformMatrix: null,
    isUp: <PERSON>olean
  },
  data: {
    loaded: true,
    arReady: false,
    activeAnchorList: [],
    isMarkerTrackerOn: false,
    isArCoordSystemInit: false,
    isTracking: false, // 是否正在实时进行用户位置追踪
    cameraTrackingState: -1
  },
  cameraTrs: null,
  arRawData: null,
  camera: null,
  cameraStateManager: null,
  root: null,
  lifetimes: {
    attached() {
      console.log('data', this.data)
    },
    detached() {
      this.pauseTracking()
      this.stopSpawningCameraMesh()
    }
  },
  methods: {
    handleReady({detail}) {
      this.scene = detail.value;
      this.root = this.scene.getElementById('root'); 
      this.mat = new (wx.getXrFrameSystem().Matrix4)();
    },
    handleARReady: async function({detail}) {
      console.log('arReady', this.scene.ar.arVersion);
      this.setData({ arReady: true })
      this.cameraTrs = this.scene.getElementById('camera').getComponent(xr.Transform)
      this.camera = this.scene.getElementById('camera').getComponent(xr.Camera)
      // this.planeTracker = this.scene.getElementById('plane').getComponent(xr.ARTracker)
      this.arRawData = this.scene.ar.getARRawData()
      this.cameraStateManager = new CameraStateManager()
      this.resumeTracking()
    },
    handleOnTick(deltaTime) {  
      try {
        let worldPos = this.cameraTrs.position
        let worldQuat = this.cameraTrs.quaternion
        let originalPose = {
          position: xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z),
          quaternion: xr.Quaternion.fromEulerAngles(xr.Vector3.createFromNumber(this.cameraTrs.rotation.x, this.cameraTrs.rotation.y, this.cameraTrs.rotation.z))
        }
        if (!prevPos) {
          prevPos = xr.Vector3.createFromNumber(worldPos.x, worldPos.y, worldPos.z)
        }
        const currCameraTrackingState = this.cameraStateManager.getCameraState(prevPos, worldPos)
        if (!this.data.isArCoordSystemInit) {
          if (currCameraTrackingState === 1)
          {
            console.log('isArActive: ar init')
            this.setData({
              isArCoordSystemInit: true,
              cameraTrackingState: 1
            })
            this.arSystemTransformMatrix = markerTransform.composeRotationMappingMatrix()
            this.triggerEvent('arInit')
          }
          // console.log("摄像机位姿: position: x: "+this.cameraTrs.position.x+", y: "+this.cameraTrs.position.y+", z: "+this.cameraTrs.position.z+"; rotation: x: "+this.cameraTrs.rotation.x+", y: "+this.cameraTrs.rotation.y+", z: "+this.cameraTrs.rotation.z)
        } else {
          if (this.data.cameraTrackingState === 1 && currCameraTrackingState === 2) {
              this.setData({
                cameraTrackingState: 2
              })
              this.triggerEvent('arLost')
              return
          }
            
          if (this.data.cameraTrackingState === 2 && currCameraTrackingState === 1) {
            this.setData({
              cameraTrackingState: 1
            })
            this.triggerEvent('arTracked')
          }
          // console.log("prevPos: x: "+prevPos.x+", y: "+prevPos.y+", z: "+prevPos.z)
          // console.log("worldPos: x: "+worldPos.x+", y: "+worldPos.y+", z: "+worldPos.z)
          prevPos.setValue(worldPos.x, worldPos.y, worldPos.z)
          let arSystemCorrectedPose = null
          if (this.arSystemTransformMatrix) {
            arSystemCorrectedPose = markerTransform.getTransformedPose(Object.values(this.arSystemTransformMatrix), originalPose)
            this.updateCameraPose(arSystemCorrectedPose)
          }
        this.triggerEvent('originalCameraPoseTick', {
            cameraPos: originalPose.position,
            cameraQuat: originalPose.quaternion,
            arRawData: this.arRawData
          })
          //定位成功后相机位姿不需要再变换
          // if (this.data.initialVpsTracked && this.data.transformMatrix) {
          //   let vpsCorrectedPose = markerTransform.getTransformedPose(Object.values(this.data.transformMatrix), arSystemCorrectedPose)
          //   this.updateCameraPose(vpsCorrectedPose)
          // }
          this.triggerEvent('cameraPoseTick', {
            cameraPos: arSystemCorrectedPose.position,
            cameraQuat: arSystemCorrectedPose.quaternion,
            arRawData: this.arRawData
          })
          //console.log("摄像机位姿"+JSON.stringify(cameraTrs.position))

          // 更新所有文字的billboard效果，使用AR校正后的相机位置
          this.updateTextBillboard(arSystemCorrectedPose ? arSystemCorrectedPose.position : null)
        }
        // console.log('cameraState: '+currCameraTrackingState)
      } catch(err) {
        console.log('[onTick] error: ', err)
      }
    },
    updateCameraPose(correctedPose) {
      try {
        if (!correctedPose || !correctedPose.position || !correctedPose.quaternion) {
          throw new Error("缺少位置或旋转数据");
        }
        this.cameraTrs.position.set(xr.Vector3.createFromNumber(correctedPose.position.x, correctedPose.position.y, correctedPose.position.z))
        this.cameraTrs.quaternion.set(correctedPose.quaternion)
      } catch(err) {
        console.log('[updateCameraPose]: '+err)
      }
    },
    pauseTracking() {
      if (this.data.isTracking) {
        this.scene.event.clear('tick');
        this.onTickCallback = null;  // 清空回调引用
        this.setData({
          isTracking: false
        })
      }
    },
    resumeTracking() {
      if (!this.data.isTracking) {
        if (!this.cameraTrs) {
          console.error('Cannot find cameraTrs')
          return
        }
        if (!this.arRawData) {
          console.error('Cannot find arRawData')
          return
        }

        this.onTickCallback = deltaTime => this.handleOnTick(deltaTime);
        this.scene.event.add('tick', this.onTickCallback);
        this.setData({
          isTracking: true
        })
      }
    },
    refreshActiveAnchorList(anchorList) {
      try {
        if (!Array.isArray(anchorList)) {
          console.error("anchorList is not an array or is undefined");
          return;
        }
        //const activeAnchorList = []
        anchorList.forEach(marker => {
          if (marker.isActive) {
            this.gltfHandler(marker)
          } else {
            this.hideAnchor(marker.id)
          }
        })
        // console.log('activeAnchorList: '+activeAnchorList.map(anchor => anchor.name))
        // const isMarkerModeOn = anchorList.length > 0
        // this.setData({
        //   isMarkerTrackerOn: isMarkerModeOn,
        //   activeAnchorList: activeAnchorList
        // })
      } catch (err) {
        console.log('[refreshActiveAnchorList] error: ', err)
      }
    },
    getPlaneTrackerTransform() {
      // 从 tracker 获取 Transform 组件
      const planeTrackerTransform = this.scene.getElementById('plane').getComponent(xr.Transform)
      if (planeTrackerTransform) {
        // 获取 Transform 的位置、旋转
        const position = {
          x: planeTrackerTransform.worldPosition.x,
          y: planeTrackerTransform.worldPosition.y,
          z: planeTrackerTransform.worldPosition.z
        }
        const euler = planeTrackerTransform.worldQuaternion.toEulerAngles()
        const rotation = {
          x: euler.x,
          y: euler.y,
          z: euler.z
        };
        // console.log('Position:', position);
        // console.log('Rotation:', rotation);
        return {position,rotation}
      } else {
        console.warn("Transform component not found on tracker.");
        return null
      }
    },
    async loadGLTFSingle(marker) {
      const scene = this.scene;    
      if (!this.root) {
        console.error("Root node not found");
        return;
      }
    
      console.log(`Loading GLTF: ${marker.id}`);

      const lockItemNodeId = `lockItem-${marker.id}`
      let lockItemEle = this.root.getChildByName(lockItemNodeId)
    
      if (!lockItemEle) {
        // Create a new lock item element
        lockItemEle = scene.createElement(xr.XRNode, {
          id: lockItemNodeId,
          name: lockItemNodeId,
          position: `${marker.position.x} ${marker.position.y} ${marker.position.z}`,
          // rotation: `${marker.rotation.x} ${marker.rotation.y} ${marker.rotation.z}`,
        });
      }

      const xrText = scene.createElement(xr.XRText, {
        position: "0 0 0",
        uniforms: "u_baseColorFactor:1.0 1.0 1.0 1",
        size: "0.3",
        anchor: "0.5 0.5",
        value: `${marker.name}`
      })

      // 尝试手动设置ID属性
      try {
        xrText.id = `text-${marker.id}`;
        xrText.nodeId = `text-${marker.id}`;
        xrText.name = `text-${marker.id}`;
      } catch (e) {
        console.log('[Billboard] Failed to set text node properties:', e);
      }

        // const mesh = scene.createElement(xr.XRMesh, {
        //   rotation: "-90 0 0",
        //   position: "0 0 0",
        //   scale: `${marker.scale.x*0.21} ${marker.scale.y} ${marker.scale.z*0.297}`,
        //   geometry: "plane",
        //   material: "standard-mat",
        //   uniforms: "u_baseColorMap: planeTexture",
        //   states: "cullOn: false"
        // });

      lockItemEle.addChild(xrText)
      this.root.addChild(lockItemEle)
      console.log(`GLTF asset loaded and added to scene: ${marker.id}`);
      this.setData({ gltfLoaded: true });

        // try {
        //   // Load or fetch GLTF asset
        //   let gltfAsset = await this.loadGLTFAsset(gltfItem);
        //   if (!gltfAsset) {
        //     console.error(`Failed to load GLTF asset for: ${gltfItem.id}`);
        //     return;
        //   }
      
        //   // Add GLTF instances for each world pose
        //   const worldPoses = gltfItem.worldPoses || [];
        //   const gltfElements = worldPoses.map(pose => {
        //     const gltf = scene.createElement(xr.XRGLTF, {
        //       position: pose.position,
        //       scale: pose.scale,
        //       rotation: pose.rotation,
        //       'anim-autoplay': '',
        //     });
      
        //     gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value });
        //     return gltf;
        //   });
      
        //   // Attach all children at once
        //   gltfElements.forEach(child => lockItemEle.addChild(child));
        //   root.addChild(lockItemEle);
      
        //   console.log(`GLTF asset loaded and added to scene: ${gltfItem.id}`);
        //   this.setData({ gltfLoaded: true });
        // } catch (error) {
        //   console.error(`Error loading GLTF item ${gltfItem.id}:`, error);
        // }
    },
    hideAnchor(anchorId) {
      if (!this.root) {
        console.error("Root node not found");
        return;
      }
      const lockItemNodeId = `lockItem-${anchorId}`
      // Disable all existing lock items
      const lockItemToHide = this.root.getChildByName(lockItemNodeId)
      if (lockItemToHide) {
        console.log('hide other lockItem: '+lockItemNodeId)
        const xrTextEle = lockItemToHide.getChildAtIndex(0)
        lockItemToHide.removeChild(xrTextEle)
      }
    },
    async gltfHandler(markerInfo) {
      this.setData({
        gltfLoaded: false
      })
      try {
        await this.loadGLTFSingle(markerInfo)
      } catch (err) {
        console.log('[gltf load] error: ', err)
      }
    },
    handleTrackerState({detail}) {
      console.log('handleTrackerState')
      const tracker = detail.value
      // 获取当前状态和错误信息
      const {state, errorMessage} = tracker;
      const markerId = tracker.el.id.substring(7)

      if (state === 2) {
        if (markerId != 'entryMarker' && this.data.initialVpsTracked)
        {
          this.data.activeAnchorList.forEach(async (marker) => {
            if (marker.id === markerId) {
              await this.gltfHandler(marker)
          }
          })
        } else {
          this.recordPlaneTrackerTransform(markerId)
        }
    }
    },
    spawnCameraPoseMesh() {
      const cameraEuler = this.cameraTrs.quaternion.toEulerAngles()
      const cameraPos = this.cameraTrs.position

      const meshNode = this.scene.createElement(xr.XRNode, {
        rotation: `${cameraEuler.x*180/Math.PI} ${cameraEuler.y*180/Math.PI} ${cameraEuler.z*180/Math.PI}`,
        position: `${cameraPos.x} ${cameraPos.y} ${cameraPos.z}`,
      })

      const meshX = this.scene.createElement(xr.XRMesh, {
        position: `0.05 0 0`,
        scale: `0.1 0.02 0.02`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.7 0.3 0.3 1"
      });
      const meshY = this.scene.createElement(xr.XRMesh, {
        position: `0 0.05 0`,
        scale: `0.02 0.1 0.02`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.3 0.7 0.3 1"
      });
      const meshZ = this.scene.createElement(xr.XRMesh, {
        position: `0 0 0.05`,
        scale: `0.02 0.02 0.1`,
        geometry: "cube",
        uniforms: "u_baseColorFactor:0.3 0.3 0.7 1"
      });
      const root = this.scene.getElementById('root');
      if (!root) {
        console.error('Root element not found');
        return;
      }
      meshNode.addChild(meshX);
      meshNode.addChild(meshY);
      meshNode.addChild(meshZ);
      root.addChild(meshNode)
    },
    stopSpawningCameraMesh() {
      clearInterval(this.data.spawnCameraMeshIntervalId)
      this.setData({
        spawnCameraMeshIntervalId: null
      })
      this.spawnCameraPoseMesh()
    },
    async spawnAnchorItem(marker) {
      if (!marker || !marker.position || !marker.rotation || !marker.scale) {
        console.error('Invalid marker data');
        return;
      }
      const root = this.scene.getElementById('root');
      if (!root) {
        console.error('Root element not found');
        return;
      }

      let meshNode = root.getChildByName('anchor-'+marker.id)
      if (meshNode) {
        const meshNodeTrs = meshNode.getComponent(xr.Transform)
        meshNodeTrs.position.setValue(marker.position.x, marker.position.y, marker.position.z)
        meshNodeTrs.rotation.setValue(marker.rotation.x*Math.PI/180, marker.rotation.y*Math.PI/180, marker.rotation.z*Math.PI/180)
      } else {
        meshNode = this.scene.createElement(xr.XRNode, {
          id: marker.id,
          name: marker.name,
          position: `${marker.position.x} ${marker.position.y} ${marker.position.z}`,
          rotation: `${marker.rotation.x} ${marker.rotation.y} ${marker.rotation.z}`,
        })
      
        const mesh = this.scene.createElement(xr.XRMesh, {
          rotation: "-90 0 0",
          position: "0 0 0",
          scale: `${marker.scale.x*0.21} ${marker.scale.y} ${marker.scale.z*0.297}`,
          geometry: "plane",
          material: "standard-mat",
          uniforms: "u_baseColorMap: planeTexture",
          states: "cullOn: false"
        });

        // // Load or fetch GLTF asset
			  // let gltfAsset = await this.loadGLTFAsset(marker.assetId, marker.url);
			  // if (!gltfAsset) {
				// console.error(`Failed to load GLTF asset for: ${marker.assetId}`);
				// return;
			  // }
		
			  // // Add GLTF instances for each world pose
				// const gltf = this.scene.createElement(xr.XRGLTF, {
				//   name: `gltf-${marker.id}`,
				//   position: `0 0 0`,
				//   rotation: `0 0 180`,
				//   scale: `${marker.scale.x} ${marker.scale.y} ${marker.scale.z}`,
				//   'anim-autoplay': '',
				// });
				
				// gltf.getComponent(xr.GLTF).setData({ model: gltfAsset.value });
  
        meshNode.addChild(mesh)
        root.addChild(meshNode);
      }
    },
    recordPlaneTrackerTransform(markerId) {
      const trackerTrs = this.getPlaneTrackerTransform()
      this.triggerEvent('trackerPositionReceived', {
        id: markerId,
        position: trackerTrs.position,
        rotation: trackerTrs.rotation
      })
    },
    async loadGLTFAsset(assetId, url) {
      const scene = this.scene;
	  
	  if (!scene) {
		  console.log('scene is undefined')
		  return
	  }
      // Check if asset already exists
      let gltfAsset = scene.assets.getAssetWithState('gltf', assetId);
      console.log(assetId+' gltfAsset is null: '+(!gltfAsset.value)+', state: '+gltfAsset.state)
      if (!gltfAsset || !gltfAsset.value) {
        try {
			console.log('loadGLTFAsset id: '+assetId)
		  // Load asset if not found
		  gltfAsset = await scene.assets.loadAsset({
			type: 'gltf',
			assetId,
			src: url,
		  });
		} catch (err) {
		  console.error("Asset loading error:", err);
		  return null;
		}
      }
      return gltfAsset;
    },
    // 更新所有文字的billboard效果，使其始终面向相机
    updateTextBillboard(correctedCameraPos) {
      if (!this.root) {
        return;
      }

      try {
        // 使用AR校正后的相机位置，如果没有则使用原始位置
        const cameraPos = correctedCameraPos || (this.cameraTrs ? this.cameraTrs.position : null);
        if (!cameraPos) {
          console.log('[Billboard] No camera position available');
          return;
        }

        console.log(`[Billboard Debug] Using camera position: (${cameraPos.x.toFixed(3)}, ${cameraPos.y.toFixed(3)}, ${cameraPos.z.toFixed(3)})`);

        // 遍历所有lockItem节点
        const children = this.root.getChildrenByFilter((childEle) => {
            return childEle.name && childEle.name.startsWith('lockItem-')
        });
        let textNodesUpdated = 0;

        // 每60帧打印一次调试信息
        if (this.billboardDebugCounter === undefined) {
          this.billboardDebugCounter = 0;
        }
        this.billboardDebugCounter++;
        const shouldLog = this.billboardDebugCounter % 60 === 0;

        if (shouldLog) {
          console.log(`[Billboard Debug] Found ${children.length} lockItem children`);
        }

        for (let i = 0; i < children.length; i++) {
          const lockItem = children[i];
          if (shouldLog) {
            console.log(`[Billboard Debug] Processing lockItem: ${lockItem.name}`);
          }
            // 获取文字节点（第一个子节点）
            const textNode = lockItem.getChildAtIndex(0);
            if (shouldLog) {
              console.log(`[Billboard Debug] TextNode found:`, textNode);
            }
            // 尝试多种方式检查文字节点
            // const nodeId = textNode && (textNode.id || textNode.nodeId || textNode.name);
            if (textNode) {
              // 获取lockItem的世界位置
              const lockItemTransform = lockItem.getComponent(xr.Transform);
              if (!lockItemTransform || !lockItemTransform.worldPosition) {
                continue;
              }
              const textPos = lockItemTransform.worldPosition;
              if (shouldLog) {
                console.log(`[Billboard Debug] Text position: (${textPos.x.toFixed(3)}, ${textPos.y.toFixed(3)}, ${textPos.z.toFixed(3)})`);
              }

              // 计算从文字到相机的方向向量
              const dirX = cameraPos.x - textPos.x;
              const dirZ = cameraPos.z - textPos.z;
              if (shouldLog) {
                console.log(`[Billboard Debug] Direction vector: dirX=${dirX.toFixed(3)}, dirZ=${dirZ.toFixed(3)}`);
              }

              // 避免除零错误
              if (Math.abs(dirX) < 0.001 && Math.abs(dirZ) < 0.001) {
                continue;
              }

              // 计算Y轴旋转角度（绕Y轴旋转使文字面向相机）
              let yRotation = Math.atan2(dirX, dirZ);
              if (shouldLog) {
                console.log(`[Billboard Debug] Calculated Y rotation: ${(yRotation * 180 / Math.PI).toFixed(1)} degrees`);
              }

              // 更新文字的旋转，保持Z轴180度翻转以确保文字正向显示
              const textTransform = textNode.getComponent(xr.Transform);
              if (textTransform && textTransform.rotation) {
                textTransform.rotation.setValue(0, yRotation, 0);
                if (shouldLog) {
                  console.log(`[Billboard Debug] Updated text rotation for ${nodeId}`);
                }
              } else if (shouldLog) {
                console.log(`[Billboard Debug] Failed to get text transform for ${nodeId}`);
              }

              textNodesUpdated++;
            }
        }

        // 打印最终统计信息
        if (shouldLog) {
          console.log(`[Billboard] Updated ${textNodesUpdated} text nodes, total children: ${children.length}`);
          if (textNodesUpdated > 0) {
            console.log(`[Billboard] Camera position: (${cameraPos.x.toFixed(2)}, ${cameraPos.y.toFixed(2)}, ${cameraPos.z.toFixed(2)})`);
          }
        }
      } catch (err) {
        console.log('[updateTextBillboard] error: ', err);
      }
    },
  },
})